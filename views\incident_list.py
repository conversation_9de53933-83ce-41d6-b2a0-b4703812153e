"""
قائمة الحوادث
"""
import os
import sys
import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QComboBox,
    QDateEdit, QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QAbstractItemView, QMessageBox, QFrame, QGroupBox, QFormLayout,
    QScrollArea, QSpinBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from controllers.incident_controller import IncidentController

class IncidentListWidget(QWidget):
    """ويدجت قائمة الحوادث"""
    
    # إشارة تعديل الحادثة
    edit_incident = pyqtSignal(int)
    
    def __init__(self, incident_controller: IncidentController):
        """
        تهيئة ويدجت قائمة الحوادث
        
        Args:
            incident_controller: متحكم الحوادث
        """
        super().__init__()
        
        self.incident_controller = incident_controller
        self.current_page = 1
        self.per_page = 20
        self.total_incidents = 0
        
        # إنشاء التخطيط
        self.setup_ui()
        
        # تحميل الحوادث
        self.refresh_list()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # ويدجت المحتوى
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # مجموعة البحث
        search_group = QGroupBox("بحث")
        search_layout = QVBoxLayout()
        
        # نموذج البحث
        form_layout = QFormLayout()
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)
        
        # اسم التلميذ
        self.student_name_search = QLineEdit()
        form_layout.addRow("اسم التلميذ:", self.student_name_search)
        
        # المؤسسة
        self.institution_search = QLineEdit()
        form_layout.addRow("المؤسسة:", self.institution_search)
        
        # السلك
        self.education_level_search = QComboBox()
        self.education_level_search.addItem("الكل", "")
        self.education_level_search.addItems(["ابتدائي", "إعدادي", "تأهيلي"])
        form_layout.addRow("السلك:", self.education_level_search)
        
        # نوع الحادثة
        self.incident_type_search = QComboBox()
        self.incident_type_search.addItem("الكل", "")
        self.incident_type_search.addItems(["حادثة مدرسية", "حادثة تنقل", "حادثة رياضية"])
        form_layout.addRow("نوع الحادثة:", self.incident_type_search)
        
        # الحالة
        self.status_search = QComboBox()
        self.status_search.addItem("الكل", "")
        self.status_search.addItems(["قيد التسوية", "تمت التسوية", "مرفوض", "تم الدفع"])
        form_layout.addRow("الحالة:", self.status_search)
        
        # تاريخ البداية
        self.start_date_search = QDateEdit()
        self.start_date_search.setCalendarPopup(True)
        self.start_date_search.setDate(QDate.currentDate().addMonths(-1))
        self.start_date_search.setDisplayFormat("yyyy/MM/dd")
        form_layout.addRow("من تاريخ:", self.start_date_search)
        
        # تاريخ النهاية
        self.end_date_search = QDateEdit()
        self.end_date_search.setCalendarPopup(True)
        self.end_date_search.setDate(QDate.currentDate())
        self.end_date_search.setDisplayFormat("yyyy/MM/dd")
        form_layout.addRow("إلى تاريخ:", self.end_date_search)
        
        search_layout.addLayout(form_layout)
        
        # أزرار البحث
        search_buttons_layout = QHBoxLayout()
        
        # زر البحث
        self.search_button = QPushButton("بحث")
        self.search_button.clicked.connect(self.search_incidents)
        search_buttons_layout.addWidget(self.search_button)
        
        # زر إعادة تعيين
        self.reset_button = QPushButton("إعادة تعيين")
        self.reset_button.clicked.connect(self.reset_search)
        search_buttons_layout.addWidget(self.reset_button)
        
        search_layout.addLayout(search_buttons_layout)
        
        search_group.setLayout(search_layout)
        content_layout.addWidget(search_group)
        
        # جدول الحوادث
        self.incidents_table = QTableWidget()
        self.incidents_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.incidents_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.incidents_table.setAlternatingRowColors(True)
        self.incidents_table.verticalHeader().setVisible(False)
        self.incidents_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.incidents_table.setColumnCount(8)
        self.incidents_table.setHorizontalHeaderLabels([
            "رقم الملف", "اسم التلميذ", "المؤسسة", "السلك", "نوع الحادثة", "الحالة", "تاريخ التسجيل", "إجراءات"
        ])
        
        # ربط حدث النقر المزدوج على الصف
        self.incidents_table.cellDoubleClicked.connect(self.on_row_double_clicked)
        
        content_layout.addWidget(self.incidents_table)
        
        # شريط التنقل بين الصفحات
        pagination_layout = QHBoxLayout()
        
        # زر الصفحة السابقة
        self.prev_page_button = QPushButton("الصفحة السابقة")
        self.prev_page_button.clicked.connect(self.prev_page)
        pagination_layout.addWidget(self.prev_page_button)
        
        # معلومات الصفحة
        self.page_info_label = QLabel()
        pagination_layout.addWidget(self.page_info_label)
        
        # زر الصفحة التالية
        self.next_page_button = QPushButton("الصفحة التالية")
        self.next_page_button.clicked.connect(self.next_page)
        pagination_layout.addWidget(self.next_page_button)
        
        # عدد العناصر في الصفحة
        per_page_layout = QHBoxLayout()
        per_page_layout.addWidget(QLabel("عدد العناصر في الصفحة:"))
        
        self.per_page_input = QSpinBox()
        self.per_page_input.setMinimum(10)
        self.per_page_input.setMaximum(100)
        self.per_page_input.setSingleStep(10)
        self.per_page_input.setValue(self.per_page)
        self.per_page_input.valueChanged.connect(self.on_per_page_changed)
        per_page_layout.addWidget(self.per_page_input)
        
        pagination_layout.addLayout(per_page_layout)
        
        content_layout.addLayout(pagination_layout)
        
        # تعيين ويدجت المحتوى لمنطقة التمرير
        scroll_area.setWidget(content_widget)
        
        # إضافة منطقة التمرير إلى التخطيط الرئيسي
        main_layout.addWidget(scroll_area)
    
    def refresh_list(self):
        """تحديث قائمة الحوادث"""
        
        # البحث عن الحوادث
        self.search_incidents()
    
    def search_incidents(self):
        """البحث عن الحوادث حسب معايير البحث"""
        
        # جمع معايير البحث
        student_name = self.student_name_search.text().strip()
        institution = self.institution_search.text().strip()
        education_level = self.education_level_search.currentText()
        if education_level == "الكل":
            education_level = None
        
        incident_type = self.incident_type_search.currentText()
        if incident_type == "الكل":
            incident_type = None
        
        status = self.status_search.currentText()
        if status == "الكل":
            status = None
        
        start_date = self.start_date_search.date().toString("yyyy/MM/dd")
        end_date = self.end_date_search.date().toString("yyyy/MM/dd")
        
        # البحث عن الحوادث
        incidents, total = self.incident_controller.search_incidents(
            student_name=student_name if student_name else None,
            institution=institution if institution else None,
            education_level=education_level,
            incident_type=incident_type,
            status=status,
            start_date=start_date,
            end_date=end_date,
            page=self.current_page,
            per_page=self.per_page
        )
        
        # تحديث إجمالي عدد الحوادث
        self.total_incidents = total
        
        # عرض الحوادث في الجدول
        self.display_incidents(incidents)
        
        # تحديث معلومات الصفحة
        self.update_pagination()
    
    def display_incidents(self, incidents):
        """
        عرض الحوادث في الجدول
        
        Args:
            incidents: قائمة الحوادث
        """
        # مسح الجدول
        self.incidents_table.setRowCount(0)
        
        # إضافة الحوادث إلى الجدول
        for row, incident in enumerate(incidents):
            self.incidents_table.insertRow(row)
            
            # رقم الملف
            self.incidents_table.setItem(row, 0, QTableWidgetItem(incident.get('file_number', '')))
            
            # اسم التلميذ
            self.incidents_table.setItem(row, 1, QTableWidgetItem(incident.get('student_name', '')))
            
            # المؤسسة
            self.incidents_table.setItem(row, 2, QTableWidgetItem(incident.get('institution', '')))
            
            # السلك
            self.incidents_table.setItem(row, 3, QTableWidgetItem(incident.get('education_level', '')))
            
            # نوع الحادثة
            self.incidents_table.setItem(row, 4, QTableWidgetItem(incident.get('incident_type', '')))
            
            # الحالة
            self.incidents_table.setItem(row, 5, QTableWidgetItem(incident.get('status', '')))
            
            # تاريخ التسجيل
            created_at = incident.get('created_at')
            if created_at:
                if isinstance(created_at, str):
                    created_at_text = created_at
                elif isinstance(created_at, datetime.datetime):
                    created_at_text = created_at.strftime('%Y/%m/%d')
                else:
                    created_at_text = ''
            else:
                created_at_text = ''
            
            self.incidents_table.setItem(row, 6, QTableWidgetItem(created_at_text))
            
            # إجراءات
            actions_layout = QHBoxLayout()
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(5)
            # محاذاة الأيقونات في الوسط
            actions_layout.setAlignment(Qt.AlignCenter)

            # زر التعديل
            edit_button = QPushButton("تعديل")
            edit_button.setProperty('incident_id', incident.get('id'))
            edit_button.clicked.connect(self.on_edit_button_clicked)
            actions_layout.addWidget(edit_button)

            # زر الحذف (للمدير فقط)
            if self.incident_controller.auth_controller.is_admin():
                delete_button = QPushButton("حذف")
                delete_button.setProperty('incident_id', incident.get('id'))
                delete_button.clicked.connect(self.on_delete_button_clicked)
                actions_layout.addWidget(delete_button)

            # إنشاء ويدجت للإجراءات
            actions_widget = QWidget()
            actions_widget.setLayout(actions_layout)

            # إضافة ويدجت الإجراءات إلى الجدول
            self.incidents_table.setCellWidget(row, 7, actions_widget)
    
    def update_pagination(self):
        """تحديث معلومات التنقل بين الصفحات"""
        
        # حساب عدد الصفحات
        total_pages = (self.total_incidents + self.per_page - 1) // self.per_page
        
        # تحديث معلومات الصفحة
        self.page_info_label.setText(f"الصفحة {self.current_page} من {total_pages} (إجمالي: {self.total_incidents})")
        
        # تعطيل/تفعيل أزرار التنقل
        self.prev_page_button.setEnabled(self.current_page > 1)
        self.next_page_button.setEnabled(self.current_page < total_pages)
    
    def reset_search(self):
        """إعادة تعيين معايير البحث"""
        
        # إعادة تعيين حقول البحث
        self.student_name_search.clear()
        self.institution_search.clear()
        self.education_level_search.setCurrentIndex(0)
        self.incident_type_search.setCurrentIndex(0)
        self.status_search.setCurrentIndex(0)
        self.start_date_search.setDate(QDate.currentDate().addMonths(-1))
        self.end_date_search.setDate(QDate.currentDate())
        
        # إعادة تعيين الصفحة الحالية
        self.current_page = 1
        
        # تحديث القائمة
        self.refresh_list()
    
    def prev_page(self):
        """الانتقال إلى الصفحة السابقة"""
        
        if self.current_page > 1:
            self.current_page -= 1
            self.search_incidents()
    
    def next_page(self):
        """الانتقال إلى الصفحة التالية"""
        
        total_pages = (self.total_incidents + self.per_page - 1) // self.per_page
        
        if self.current_page < total_pages:
            self.current_page += 1
            self.search_incidents()
    
    def on_per_page_changed(self, value):
        """
        معالجة تغيير عدد العناصر في الصفحة
        
        Args:
            value: القيمة الجديدة
        """
        self.per_page = value
        self.current_page = 1
        self.search_incidents()
    
    def on_row_double_clicked(self, row, column):
        """
        معالجة النقر المزدوج على صف
        
        Args:
            row: رقم الصف
            column: رقم العمود
        """
        # الحصول على معرف الحادثة
        file_number_item = self.incidents_table.item(row, 0)
        if file_number_item:
            file_number = file_number_item.text()
            incident = self.incident_controller.get_incident_by_file_number(file_number)
            
            if incident:
                # إرسال إشارة تعديل الحادثة
                self.edit_incident.emit(incident.get('id'))
    
    def on_edit_button_clicked(self):
        """معالجة النقر على زر التعديل"""
        
        # الحصول على معرف الحادثة
        button = self.sender()
        incident_id = button.property('incident_id')
        
        # إرسال إشارة تعديل الحادثة
        self.edit_incident.emit(incident_id)
    
    def on_delete_button_clicked(self):
        """معالجة النقر على زر الحذف"""
        
        # التحقق من صلاحيات المدير
        if not self.incident_controller.auth_controller.is_admin():
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية حذف الحوادث")
            return
        
        # الحصول على معرف الحادثة
        button = self.sender()
        incident_id = button.property('incident_id')
        
        # التأكيد قبل الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد",
            "هل أنت متأكد من رغبتك في حذف هذه الحادثة؟ لا يمكن التراجع عن هذه العملية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # حذف الحادثة
            success = self.incident_controller.delete_incident(incident_id)
            
            if success:
                QMessageBox.information(self, "معلومات", "تم حذف الحادثة بنجاح")
                self.refresh_list()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف الحادثة")
