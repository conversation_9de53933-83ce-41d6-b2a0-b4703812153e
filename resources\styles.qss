/* أنماط CSS للتطبيق */

/* النمط العام */
QWidget {
    font-family: Arial;
    font-size: 10pt;
    background-color: #f5f5f5;
}

/* العنوان الرئيسي */
QLabel#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #2c3e50;
    padding: 10px;
}

/* الأزرار */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    min-width: 100px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #1c6ea4;
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
}

/* أزرار الإجراءات */
QPushButton#actionButton {
    background-color: #2ecc71;
}

QPushButton#actionButton:hover {
    background-color: #27ae60;
}

QPushButton#actionButton:pressed {
    background-color: #1e8449;
}

/* أزرار الحذف */
QPushButton#deleteButton {
    background-color: #e74c3c;
}

QPushButton#deleteButton:hover {
    background-color: #c0392b;
}

QPushButton#deleteButton:pressed {
    background-color: #a93226;
}

/* حقول الإدخال */
QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    padding: 6px;
    background-color: white;
}

QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus {
    border: 1px solid #3498db;
}

/* القوائم المنسدلة */
QComboBox {
    padding-right: 20px;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #bdc3c7;
}

QComboBox::down-arrow {
    image: url(resources/icons/dropdown.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #bdc3c7;
    selection-background-color: #3498db;
    selection-color: white;
}

/* الجداول */
QTableView, QTableWidget {
    border: 1px solid #bdc3c7;
    gridline-color: #ecf0f1;
    selection-background-color: #3498db;
    selection-color: white;
    alternate-background-color: #ecf0f1;
}

QTableView::item, QTableWidget::item {
    padding: 5px;
}

QHeaderView::section {
    background-color: #34495e;
    color: white;
    padding: 5px;
    border: none;
}

/* القوائم */
QListView, QListWidget {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    background-color: white;
}

QListView::item, QListWidget::item {
    padding: 5px;
}

QListView::item:selected, QListWidget::item:selected {
    background-color: #3498db;
    color: white;
}

/* شريط التمرير */
QScrollBar:vertical {
    border: none;
    background-color: #ecf0f1;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #ecf0f1;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #bdc3c7;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* علامات التبويب */
QTabWidget::pane {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    top: -1px;
}

QTabBar::tab {
    background-color: #ecf0f1;
    border: 1px solid #bdc3c7;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 8px 12px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom: 1px solid white;
}

QTabBar::tab:hover:!selected {
    background-color: #d6dbdf;
}

/* مربعات الاختيار */
QCheckBox {
    spacing: 5px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
}

QCheckBox::indicator:unchecked {
    border: 1px solid #bdc3c7;
    background-color: white;
    border-radius: 2px;
}

QCheckBox::indicator:checked {
    border: 1px solid #3498db;
    background-color: #3498db;
    border-radius: 2px;
    image: url(resources/icons/check.png);
}

/* أزرار الراديو */
QRadioButton {
    spacing: 5px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
}

QRadioButton::indicator:unchecked {
    border: 1px solid #bdc3c7;
    background-color: white;
    border-radius: 8px;
}

QRadioButton::indicator:checked {
    border: 1px solid #3498db;
    background-color: white;
    border-radius: 8px;
}

QRadioButton::indicator:checked::before {
    content: "";
    display: block;
    width: 8px;
    height: 8px;
    background-color: #3498db;
    border-radius: 4px;
    margin: 3px;
}

/* شريط الحالة */
QStatusBar {
    background-color: #34495e;
    color: white;
    padding: 5px;
}

/* شريط القوائم */
QMenuBar {
    background-color: #34495e;
    color: white;
}

QMenuBar::item {
    padding: 5px 10px;
    background-color: transparent;
}

QMenuBar::item:selected {
    background-color: #2c3e50;
}

QMenu {
    background-color: white;
    border: 1px solid #bdc3c7;
}

QMenu::item {
    padding: 5px 30px 5px 20px;
}

QMenu::item:selected {
    background-color: #3498db;
    color: white;
}

/* شريط الأدوات */
QToolBar {
    background-color: #34495e;
    border: none;
    spacing: 5px;
    padding: 5px;
}

QToolButton {
    background-color: transparent;
    border: none;
    border-radius: 4px;
    padding: 5px;
}

QToolButton:hover {
    background-color: #2c3e50;
}

QToolButton:pressed {
    background-color: #1c2833;
}

/* مربع الحوار */
QDialog {
    background-color: #f5f5f5;
}

QDialog QLabel#titleLabel {
    font-size: 14pt;
    font-weight: bold;
    color: #2c3e50;
    padding: 10px;
}

/* مربع الرسائل */
QMessageBox {
    background-color: #f5f5f5;
}

QMessageBox QLabel {
    color: #2c3e50;
}

/* لوحة المعلومات */
QFrame#dashboardFrame {
    background-color: white;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
}

QLabel#dashboardTitle {
    font-size: 14pt;
    font-weight: bold;
    color: #2c3e50;
}

QLabel#dashboardValue {
    font-size: 24pt;
    font-weight: bold;
    color: #3498db;
}

QLabel#dashboardDescription {
    color: #7f8c8d;
}

/* نموذج الحادثة */
QGroupBox {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    margin-top: 10px;
    padding-top: 15px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
    background-color: #f5f5f5;
    color: #2c3e50;
    font-weight: bold;
}

/* شاشة تسجيل الدخول */
QWidget#loginWidget {
    background-color: white;
}

QLabel#loginTitle {
    font-size: 18pt;
    font-weight: bold;
    color: #2c3e50;
}

QLineEdit#usernameInput, QLineEdit#passwordInput {
    padding: 10px;
    font-size: 12pt;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
}

QPushButton#loginButton {
    padding: 10px;
    font-size: 12pt;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
}

QPushButton#loginButton:hover {
    background-color: #2980b9;
}

/* شاشة الإعدادات */
QWidget#settingsWidget {
    background-color: white;
}

QLabel#settingsTitle {
    font-size: 14pt;
    font-weight: bold;
    color: #2c3e50;
}

/* شاشة التقارير */
QWidget#reportsWidget {
    background-color: white;
}

QLabel#reportsTitle {
    font-size: 14pt;
    font-weight: bold;
    color: #2c3e50;
}
