"""
شاشة إدارة المستخدمين
"""
import os
import sys
import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QMessageBox, QDialog, QFormLayout, QComboBox, QCheckBox
)
from PyQt5.QtCore import Qt

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from controllers.auth_controller import AuthController
from models.database import UserRole

class UserDialog(QDialog):
    """مربع حوار إضافة/تعديل مستخدم"""
    
    def __init__(self, auth_controller: AuthController, user_id=None, parent=None):
        """
        تهيئة مربع حوار إضافة/تعديل مستخدم
        
        Args:
            auth_controller: متحكم المصادقة
            user_id: معرف المستخدم (اختياري، للتعديل)
            parent: الويدجت الأب
        """
        super().__init__(parent)
        
        self.auth_controller = auth_controller
        self.user_id = user_id
        self.user_data = None
        
        # إذا تم توفير معرف المستخدم، فهذا يعني أننا في وضع التعديل
        if user_id:
            self.user_data = self.auth_controller.get_user_by_id(user_id)
            self.setWindowTitle("تعديل مستخدم")
        else:
            self.setWindowTitle("إضافة مستخدم")
        
        self.setMinimumWidth(400)
        
        # إنشاء التخطيط
        self.setup_ui()
        
        # ملء النموذج بالبيانات إذا كنا في وضع التعديل
        if self.user_data:
            self.fill_form()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # نموذج المستخدم
        form_layout = QFormLayout()
        
        # اسم المستخدم
        self.username_input = QLineEdit()
        form_layout.addRow("اسم المستخدم:", self.username_input)
        
        # كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        form_layout.addRow("كلمة المرور:", self.password_input)
        
        # الاسم الكامل
        self.full_name_input = QLineEdit()
        form_layout.addRow("الاسم الكامل:", self.full_name_input)
        
        # البريد الإلكتروني
        self.email_input = QLineEdit()
        form_layout.addRow("البريد الإلكتروني:", self.email_input)
        
        # الدور
        self.role_input = QComboBox()
        self.role_input.addItems(["مستخدم", "مدير"])
        form_layout.addRow("الدور:", self.role_input)
        
        # نشط
        self.is_active_input = QCheckBox("المستخدم نشط")
        self.is_active_input.setChecked(True)
        form_layout.addRow("", self.is_active_input)
        
        layout.addLayout(form_layout)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.save_user)
        buttons_layout.addWidget(self.save_button)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
    
    def fill_form(self):
        """ملء النموذج بالبيانات الحالية للمستخدم"""
        
        if not self.user_data:
            return
        
        # ملء البيانات
        self.username_input.setText(self.user_data.get('username', ''))
        self.username_input.setReadOnly(True)  # لا يمكن تغيير اسم المستخدم
        self.full_name_input.setText(self.user_data.get('full_name', ''))
        self.email_input.setText(self.user_data.get('email', ''))
        
        # تعيين الدور
        role = self.user_data.get('role', '')
        if role == "مدير":
            self.role_input.setCurrentIndex(1)
        else:
            self.role_input.setCurrentIndex(0)
        
        # تعيين حالة النشاط
        self.is_active_input.setChecked(self.user_data.get('is_active', True))
        
        # إخفاء حقل كلمة المرور في وضع التعديل
        self.password_input.setPlaceholderText("اترك فارغًا للاحتفاظ بكلمة المرور الحالية")
    
    def save_user(self):
        """حفظ المستخدم"""
        
        # الحصول على البيانات من النموذج
        username = self.username_input.text().strip()
        password = self.password_input.text()
        full_name = self.full_name_input.text().strip()
        email = self.email_input.text().strip()
        role = UserRole.ADMIN if self.role_input.currentText() == "مدير" else UserRole.USER
        is_active = self.is_active_input.isChecked()
        
        # التحقق من الحقول المطلوبة
        if not username:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
        
        if not self.user_id and not password:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        if not full_name:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال الاسم الكامل")
            self.full_name_input.setFocus()
            return
        
        # إذا كنا في وضع التعديل
        if self.user_id:
            # تحديث المستخدم
            success = self.auth_controller.update_user(
                user_id=self.user_id,
                full_name=full_name,
                email=email,
                is_active=is_active,
                role=role
            )
            
            if success:
                QMessageBox.information(self, "معلومات", "تم تحديث المستخدم بنجاح")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تحديث المستخدم")
        else:
            # إنشاء مستخدم جديد
            success = self.auth_controller.create_user(
                username=username,
                password=password,
                full_name=full_name,
                email=email,
                role=role
            )
            
            if success:
                QMessageBox.information(self, "معلومات", "تم إنشاء المستخدم بنجاح")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء إنشاء المستخدم. قد يكون اسم المستخدم موجودًا بالفعل.")

class UserManagementWidget(QWidget):
    """ويدجت إدارة المستخدمين"""
    
    def __init__(self, auth_controller: AuthController):
        """
        تهيئة ويدجت إدارة المستخدمين
        
        Args:
            auth_controller: متحكم المصادقة
        """
        super().__init__()
        
        self.auth_controller = auth_controller
        
        # التحقق من صلاحيات المدير
        if not self.auth_controller.is_admin():
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية الوصول إلى هذه الصفحة")
            return
        
        # إنشاء التخطيط
        self.setup_ui()
        
        # تحميل المستخدمين
        self.refresh_users()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        # زر إضافة مستخدم
        add_user_button = QPushButton("إضافة مستخدم")
        add_user_button.clicked.connect(self.add_user)
        actions_layout.addWidget(add_user_button)
        
        # إضافة مساحة فارغة
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.users_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.verticalHeader().setVisible(False)
        self.users_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.users_table.setColumnCount(7)
        self.users_table.setHorizontalHeaderLabels([
            "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", "الدور", "نشط", "آخر تسجيل دخول", "إجراءات"
        ])
        # ضبط ارتفاع الصفوف لضمان المحاذاة العمودية للأيقونات
        self.users_table.verticalHeader().setDefaultSectionSize(45)
        
        layout.addWidget(self.users_table)
    
    def refresh_users(self):
        """تحديث قائمة المستخدمين"""
        
        # التحقق من صلاحيات المدير
        if not self.auth_controller.is_admin():
            return
        
        # الحصول على قائمة المستخدمين
        users = self.auth_controller.get_users()
        
        # مسح الجدول
        self.users_table.setRowCount(0)
        
        # إضافة المستخدمين إلى الجدول
        for row, user in enumerate(users):
            self.users_table.insertRow(row)
            
            # اسم المستخدم
            self.users_table.setItem(row, 0, QTableWidgetItem(user.get('username', '')))
            
            # الاسم الكامل
            self.users_table.setItem(row, 1, QTableWidgetItem(user.get('full_name', '')))
            
            # البريد الإلكتروني
            self.users_table.setItem(row, 2, QTableWidgetItem(user.get('email', '')))
            
            # الدور
            self.users_table.setItem(row, 3, QTableWidgetItem(user.get('role', '')))
            
            # نشط
            is_active = "نعم" if user.get('is_active', False) else "لا"
            self.users_table.setItem(row, 4, QTableWidgetItem(is_active))
            
            # آخر تسجيل دخول
            last_login = user.get('last_login')
            if last_login:
                if isinstance(last_login, str):
                    last_login_text = last_login
                elif isinstance(last_login, datetime.datetime):
                    last_login_text = last_login.strftime('%Y/%m/%d %H:%M')
                else:
                    last_login_text = ''
            else:
                last_login_text = ''
            
            self.users_table.setItem(row, 5, QTableWidgetItem(last_login_text))
            
            # إجراءات
            actions_layout = QHBoxLayout()
            actions_layout.setContentsMargins(5, 5, 5, 5)
            actions_layout.setSpacing(5)
            # محاذاة الأيقونات في الوسط أفقياً وعمودياً
            actions_layout.setAlignment(Qt.AlignCenter | Qt.AlignVCenter)

            # زر تعديل المستخدم
            edit_button = QPushButton("تعديل")
            edit_button.setProperty('user_id', user.get('id'))
            edit_button.clicked.connect(self.edit_user)
            actions_layout.addWidget(edit_button, 0, Qt.AlignCenter)

            # زر عرض نشاطات المستخدم
            activities_button = QPushButton("النشاطات")
            activities_button.setProperty('user_id', user.get('id'))
            activities_button.clicked.connect(self.show_user_activities)
            actions_layout.addWidget(activities_button, 0, Qt.AlignCenter)

            # إنشاء ويدجت للإجراءات
            actions_widget = QWidget()
            actions_widget.setLayout(actions_layout)
            # ضبط سياسة الحجم للويدجت
            actions_widget.setSizePolicy(actions_widget.sizePolicy().horizontalPolicy(), actions_widget.sizePolicy().Expanding)

            # إضافة ويدجت الإجراءات إلى الجدول
            self.users_table.setCellWidget(row, 6, actions_widget)
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        
        # التحقق من صلاحيات المدير
        if not self.auth_controller.is_admin():
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية إضافة مستخدم")
            return
        
        # عرض مربع حوار إضافة مستخدم
        dialog = UserDialog(self.auth_controller, parent=self)
        result = dialog.exec_()
        
        if result == QDialog.Accepted:
            # تحديث قائمة المستخدمين
            self.refresh_users()
    
    def edit_user(self):
        """تعديل مستخدم"""
        
        # التحقق من صلاحيات المدير
        if not self.auth_controller.is_admin():
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية تعديل مستخدم")
            return
        
        # الحصول على معرف المستخدم
        button = self.sender()
        user_id = button.property('user_id')
        
        # عرض مربع حوار تعديل مستخدم
        dialog = UserDialog(self.auth_controller, user_id, parent=self)
        result = dialog.exec_()
        
        if result == QDialog.Accepted:
            # تحديث قائمة المستخدمين
            self.refresh_users()
    
    def show_user_activities(self):
        """عرض نشاطات المستخدم"""
        
        # التحقق من صلاحيات المدير
        if not self.auth_controller.is_admin():
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية عرض نشاطات المستخدم")
            return
        
        # الحصول على معرف المستخدم
        button = self.sender()
        user_id = button.property('user_id')
        
        # الحصول على معلومات المستخدم
        user_info = self.auth_controller.get_user_by_id(user_id)
        
        if not user_info:
            QMessageBox.warning(self, "تنبيه", "لم يتم العثور على المستخدم")
            return
        
        # الحصول على نشاطات المستخدم
        activities = self.auth_controller.get_user_activities(user_id)
        
        # إنشاء نص النشاطات
        activities_text = f"نشاطات المستخدم: {user_info.get('username')}\n\n"
        
        if activities:
            for activity in activities:
                timestamp = activity.get('timestamp')
                if timestamp:
                    if isinstance(timestamp, str):
                        timestamp_text = timestamp
                    elif isinstance(timestamp, datetime.datetime):
                        timestamp_text = timestamp.strftime('%Y/%m/%d %H:%M:%S')
                    else:
                        timestamp_text = ''
                else:
                    timestamp_text = ''
                
                activities_text += f"{timestamp_text} - {activity.get('action')}"
                
                details = activity.get('details')
                if details:
                    activities_text += f": {details}"
                
                activities_text += "\n"
        else:
            activities_text += "لا توجد نشاطات لهذا المستخدم."
        
        # عرض النشاطات
        QMessageBox.information(self, "نشاطات المستخدم", activities_text)
